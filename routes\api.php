<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\ServiceApiController;
use App\Http\Controllers\Api\ProjectApiController;
use App\Http\Controllers\Api\TestimonialApiController;
use App\Http\Controllers\Api\ContactApiController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Public API Routes for Mobile App
Route::prefix('v1')->group(function () {
    // Services API
    Route::get('/services', [ServiceApiController::class, 'index']);
    Route::get('/services/{id}', [ServiceApiController::class, 'show']);
    
    // Projects API
    Route::get('/projects', [ProjectApiController::class, 'index']);
    Route::get('/projects/{id}', [ProjectApiController::class, 'show']);
    
    // Testimonials API
    Route::get('/testimonials', [TestimonialApiController::class, 'index']);
    
    // Contact Form API
    Route::post('/contact', [ContactApiController::class, 'store']);
    
    // Company Information API
    Route::get('/company-info', function () {
        return response()->json([
            'name' => 'Flori Construction Ltd',
            'email' => '<EMAIL>',
            'phone' => '******-567-8900',
            'address' => '123 Construction Ave, Building City, BC 12345',
            'website' => 'https://floriconstructionltd.com',
            'social_media' => [
                'facebook' => 'https://facebook.com/floriconstructionltd',
                'instagram' => 'https://instagram.com/floriconstructionltd',
                'linkedin' => 'https://linkedin.com/company/floriconstructionltd'
            ]
        ]);
    });
});
