<?php $__env->startSection('title', 'Edit Service'); ?>

<?php $__env->startSection('content'); ?>
<div class="flex justify-between items-center mb-4">
    <h1 style="font-size: 1.5rem; font-weight: 600; color: #1f2937;">Edit Service: <?php echo e($service->title); ?></h1>
    <div class="flex gap-2">
        <a href="<?php echo e(route('services.show', $service->slug)); ?>" target="_blank" class="btn btn-secondary">
            🔗 View on Website
        </a>
        <a href="<?php echo e(route('admin.services.index')); ?>" class="btn btn-secondary">
            ← Back to Services
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h3>Service Information</h3>
    </div>
    <div class="card-body">
        <form action="<?php echo e(route('admin.services.update', $service)); ?>" method="POST" enctype="multipart/form-data">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            
            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem;">
                <!-- Main Information -->
                <div>
                    <div class="form-group">
                        <label for="title" class="form-label">Service Title *</label>
                        <input 
                            type="text" 
                            id="title" 
                            name="title" 
                            value="<?php echo e(old('title', $service->title)); ?>" 
                            class="form-control" 
                            required
                        >
                        <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <small style="color: #6b7280; font-size: 0.75rem;">Current slug: <?php echo e($service->slug); ?></small>
                    </div>

                    <div class="form-group">
                        <label for="description" class="form-label">Short Description *</label>
                        <textarea 
                            id="description" 
                            name="description" 
                            rows="3" 
                            class="form-control" 
                            required
                        ><?php echo e(old('description', $service->description)); ?></textarea>
                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div class="form-group">
                        <label for="content" class="form-label">Detailed Content</label>
                        <textarea 
                            id="content" 
                            name="content" 
                            rows="8" 
                            class="form-control"
                        ><?php echo e(old('content', $service->content)); ?></textarea>
                        <?php $__errorArgs = ['content'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label for="price_from" class="form-label">Starting Price ($)</label>
                            <input 
                                type="number" 
                                id="price_from" 
                                name="price_from" 
                                value="<?php echo e(old('price_from', $service->price_from)); ?>" 
                                class="form-control" 
                                min="0" 
                                step="0.01"
                            >
                            <?php $__errorArgs = ['price_from'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="form-group">
                            <label for="sort_order" class="form-label">Sort Order</label>
                            <input 
                                type="number" 
                                id="sort_order" 
                                name="sort_order" 
                                value="<?php echo e(old('sort_order', $service->sort_order)); ?>" 
                                class="form-control" 
                                min="0"
                            >
                            <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>

                <!-- Images and Settings -->
                <div>
                    <!-- Current Image -->
                    <?php if($service->image): ?>
                        <div class="form-group">
                            <label class="form-label">Current Image</label>
                            <div style="margin-bottom: 1rem;">
                                <img src="<?php echo e($service->image_url); ?>" alt="<?php echo e($service->title); ?>" 
                                     style="width: 100%; max-width: 200px; height: auto; border-radius: 0.375rem; border: 1px solid #e5e7eb;">
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="form-group">
                        <label for="image" class="form-label">
                            <?php echo e($service->image ? 'Replace Image' : 'Service Image'); ?>

                        </label>
                        <input 
                            type="file" 
                            id="image" 
                            name="image" 
                            class="form-control" 
                            accept="image/*"
                        >
                        <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <small style="color: #6b7280; font-size: 0.75rem;">Max 2MB, JPG/PNG/GIF</small>
                    </div>

                    <!-- Current Icon -->
                    <?php if($service->icon): ?>
                        <div class="form-group">
                            <label class="form-label">Current Icon</label>
                            <div style="margin-bottom: 1rem;">
                                <img src="<?php echo e($service->icon_url); ?>" alt="<?php echo e($service->title); ?>" 
                                     style="width: 50px; height: 50px; border-radius: 0.375rem; border: 1px solid #e5e7eb;">
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="form-group">
                        <label for="icon" class="form-label">
                            <?php echo e($service->icon ? 'Replace Icon' : 'Service Icon'); ?>

                        </label>
                        <input 
                            type="file" 
                            id="icon" 
                            name="icon" 
                            class="form-control" 
                            accept="image/*"
                        >
                        <?php $__errorArgs = ['icon'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <small style="color: #6b7280; font-size: 0.75rem;">Max 1MB, JPG/PNG/GIF/SVG</small>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Settings</label>
                        
                        <div style="margin-bottom: 0.5rem;">
                            <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                <input 
                                    type="checkbox" 
                                    name="is_active" 
                                    value="1" 
                                    <?php echo e(old('is_active', $service->is_active) ? 'checked' : ''); ?>

                                >
                                <span>Active (visible on website)</span>
                            </label>
                        </div>
                        
                        <div>
                            <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                <input 
                                    type="checkbox" 
                                    name="is_featured" 
                                    value="1" 
                                    <?php echo e(old('is_featured', $service->is_featured) ? 'checked' : ''); ?>

                                >
                                <span>Featured (show on homepage)</span>
                            </label>
                        </div>
                    </div>

                    <!-- Service Stats -->
                    <div style="background: #f9fafb; padding: 1rem; border-radius: 0.375rem; margin-top: 1rem;">
                        <h4 style="font-size: 0.875rem; font-weight: 600; margin-bottom: 0.5rem; color: #374151;">Service Stats:</h4>
                        <div style="font-size: 0.75rem; color: #6b7280;">
                            <p>Created: <?php echo e($service->created_at->format('M j, Y')); ?></p>
                            <p>Last Updated: <?php echo e($service->updated_at->format('M j, Y')); ?></p>
                            <p>Status: <?php echo e($service->is_active ? 'Active' : 'Inactive'); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <div style="border-top: 1px solid #e5e7eb; margin-top: 2rem; padding-top: 1rem;">
                <div class="flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        💾 Update Service
                    </button>
                    <a href="<?php echo e(route('admin.services.index')); ?>" class="btn btn-secondary">
                        Cancel
                    </a>
                    <form action="<?php echo e(route('admin.services.destroy', $service)); ?>" method="POST" style="display: inline;" 
                          onsubmit="return confirm('Are you sure you want to delete this service? This action cannot be undone.')">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger">
                            🗑️ Delete Service
                        </button>
                    </form>
                </div>
            </div>
        </form>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\mobile-app-dev\resources\views/admin/services/edit.blade.php ENDPATH**/ ?>