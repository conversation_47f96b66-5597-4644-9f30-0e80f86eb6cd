<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Project;
use Illuminate\Http\Request;

class ProjectApiController extends Controller
{
    /**
     * Display a listing of projects
     */
    public function index()
    {
        try {
            $projects = Project::active()
                ->ordered()
                ->get()
                ->map(function ($project) {
                    return [
                        'id' => $project->id,
                        'title' => $project->title,
                        'slug' => $project->slug,
                        'description' => $project->description,
                        'client_name' => $project->client_name,
                        'location' => $project->location,
                        'completion_date' => $project->completion_date?->format('Y-m-d'),
                        'project_value' => $project->project_value,
                        'status' => $project->status,
                        'is_featured' => $project->is_featured,
                        'featured_image_url' => $project->featured_image_url,
                        'duration' => $project->duration,
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $projects,
                'message' => 'Projects retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving projects: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified project
     */
    public function show($id)
    {
        try {
            $project = Project::active()->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $project->id,
                    'title' => $project->title,
                    'slug' => $project->slug,
                    'description' => $project->description,
                    'content' => $project->content,
                    'client_name' => $project->client_name,
                    'location' => $project->location,
                    'start_date' => $project->start_date?->format('Y-m-d'),
                    'completion_date' => $project->completion_date?->format('Y-m-d'),
                    'project_value' => $project->project_value,
                    'status' => $project->status,
                    'is_featured' => $project->is_featured,
                    'featured_image_url' => $project->featured_image_url,
                    'image_urls' => $project->image_urls,
                    'duration' => $project->duration,
                    'meta_data' => $project->meta_data,
                ],
                'message' => 'Project retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Project not found'
            ], 404);
        }
    }
}
