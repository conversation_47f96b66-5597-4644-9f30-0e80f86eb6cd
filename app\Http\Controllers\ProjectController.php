<?php

namespace App\Http\Controllers;

use App\Models\Project;
use Illuminate\Http\Request;

class ProjectController extends Controller
{
    /**
     * Display a listing of projects
     */
    public function index()
    {
        $projects = Project::active()->ordered()->paginate(12);
        $featuredProjects = Project::active()->featured()->ordered()->take(3)->get();

        return view('projects.index', compact('projects', 'featuredProjects'));
    }

    /**
     * Display the specified project
     */
    public function show(Project $project)
    {
        if (!$project->is_active) {
            abort(404);
        }

        $relatedProjects = Project::active()
            ->where('id', '!=', $project->id)
            ->ordered()
            ->take(3)
            ->get();

        return view('projects.show', compact('project', 'relatedProjects'));
    }
}
