<?php $__env->startSection('title', 'Our Services'); ?>
<?php $__env->startSection('description', 'Explore our comprehensive construction services including residential, commercial construction, renovations, and project management.'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section style="background: linear-gradient(135deg, #1f2937 0%, #374151 100%); color: white; padding: 4rem 0; text-align: center;">
    <div class="container">
        <h1 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem;">
            Our Construction Services
        </h1>
        <p style="font-size: 1.125rem; opacity: 0.9; max-width: 600px; margin: 0 auto;">
            Professional construction services tailored to meet your specific needs and exceed your expectations.
        </p>
    </div>
</section>

<!-- Featured Services -->
<?php if($featuredServices->count() > 0): ?>
<section style="padding: 4rem 0; background: #f9fafb;">
    <div class="container">
        <div class="text-center mb-8">
            <h2 style="font-size: 2rem; font-weight: 700; margin-bottom: 1rem; color: #1f2937;">
                Featured Services
            </h2>
            <p style="color: #6b7280;">Our most popular construction services</p>
        </div>
        
        <div class="grid grid-cols-3">
            <?php $__currentLoopData = $featuredServices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="card">
                <?php if($service->image): ?>
                    <img src="<?php echo e($service->image_url); ?>" alt="<?php echo e($service->title); ?>" style="width: 100%; height: 200px; object-fit: cover;">
                <?php else: ?>
                    <div style="width: 100%; height: 200px; background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%); display: flex; align-items: center; justify-content: center; font-size: 3rem;">
                        🔧
                    </div>
                <?php endif; ?>
                
                <div class="p-6">
                    <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 0.5rem; color: #1f2937;">
                        <?php echo e($service->title); ?>

                    </h3>
                    <p style="color: #6b7280; margin-bottom: 1rem;">
                        <?php echo e($service->description); ?>

                    </p>
                    <?php if($service->price_from): ?>
                        <p style="font-weight: 600; color: #2563eb; margin-bottom: 1rem;">
                            Starting from $<?php echo e(number_format($service->price_from)); ?>

                        </p>
                    <?php endif; ?>
                    <a href="<?php echo e(route('services.show', $service->slug)); ?>" class="btn btn-primary" style="width: 100%;">
                        Learn More
                    </a>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- All Services -->
<section style="padding: 4rem 0;">
    <div class="container">
        <div class="text-center mb-8">
            <h2 style="font-size: 2rem; font-weight: 700; margin-bottom: 1rem; color: #1f2937;">
                All Services
            </h2>
            <p style="color: #6b7280;">Complete range of construction services</p>
        </div>
        
        <?php if($services->count() > 0): ?>
            <div class="grid grid-cols-3">
                <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="card">
                    <?php if($service->image): ?>
                        <img src="<?php echo e($service->image_url); ?>" alt="<?php echo e($service->title); ?>" style="width: 100%; height: 200px; object-fit: cover;">
                    <?php else: ?>
                        <div style="width: 100%; height: 200px; background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%); display: flex; align-items: center; justify-content: center; font-size: 3rem;">
                            <?php if($service->icon): ?>
                                <img src="<?php echo e($service->icon_url); ?>" alt="<?php echo e($service->title); ?>" style="width: 3rem; height: 3rem;">
                            <?php else: ?>
                                🔧
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="p-6">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 0.5rem;">
                            <h3 style="font-size: 1.25rem; font-weight: 600; color: #1f2937;">
                                <?php echo e($service->title); ?>

                            </h3>
                            <?php if($service->is_featured): ?>
                                <span style="background: #059669; color: white; font-size: 0.75rem; padding: 0.25rem 0.5rem; border-radius: 9999px;">
                                    Featured
                                </span>
                            <?php endif; ?>
                        </div>
                        
                        <p style="color: #6b7280; margin-bottom: 1rem;">
                            <?php echo e($service->description); ?>

                        </p>
                        
                        <?php if($service->price_from): ?>
                            <p style="font-weight: 600; color: #2563eb; margin-bottom: 1rem;">
                                Starting from $<?php echo e(number_format($service->price_from)); ?>

                            </p>
                        <?php endif; ?>
                        
                        <a href="<?php echo e(route('services.show', $service->slug)); ?>" class="btn btn-primary" style="width: 100%;">
                            Learn More
                        </a>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            
            <!-- Pagination -->
            <?php if($services->hasPages()): ?>
                <div style="margin-top: 3rem; text-align: center;">
                    <?php echo e($services->links()); ?>

                </div>
            <?php endif; ?>
        <?php else: ?>
            <div style="text-align: center; padding: 3rem; color: #6b7280;">
                <p style="font-size: 1.125rem;">No services available at the moment.</p>
                <p>Please check back later or contact us for more information.</p>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Call to Action -->
<section style="padding: 4rem 0; background: #2563eb; color: white; text-align: center;">
    <div class="container">
        <h2 style="font-size: 2rem; font-weight: 700; margin-bottom: 1rem;">
            Need a Custom Solution?
        </h2>
        <p style="font-size: 1.125rem; margin-bottom: 2rem; opacity: 0.9;">
            Don't see exactly what you're looking for? We provide custom construction solutions tailored to your specific needs.
        </p>
        <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
            <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary" style="background: white; color: #2563eb;">
                Get Custom Quote
            </a>
            <a href="tel:******-567-8900" class="btn btn-secondary" style="background: transparent; border: 2px solid white;">
                📞 Call Us Now
            </a>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\mobile-app-dev\resources\views/services/index.blade.php ENDPATH**/ ?>