<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e(config('app.name')); ?> - Professional Construction Services</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }
        
        .logo {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .tagline {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .success-message {
            background: rgba(255, 255, 255, 0.95);
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .success-icon {
            font-size: 4rem;
            color: #27ae60;
            margin-bottom: 1rem;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .feature-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 1rem;
        }
        
        .feature-description {
            color: #7f8c8d;
            line-height: 1.6;
        }
        
        .status-info {
            background: rgba(255, 255, 255, 0.95);
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-top: 2rem;
        }
        
        .status-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .status-item {
            text-align: center;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .status-item.completed {
            background: #d4edda;
            color: #155724;
        }
        
        .status-item.pending {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🏗️ <?php echo e(config('app.name')); ?></div>
            <div class="tagline">Professional Construction Services</div>
        </div>
        
        <div class="success-message">
            <div class="success-icon">✅</div>
            <h1>Laravel Application Successfully Installed!</h1>
            <p>Your Flori Construction Ltd website is now ready for development.</p>
        </div>
        
        <div class="features">
            <div class="feature-card">
                <div class="feature-icon">🌐</div>
                <div class="feature-title">Frontend Website</div>
                <div class="feature-description">
                    Modern, responsive website with services, projects, and contact forms
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">⚙️</div>
                <div class="feature-title">Admin Panel</div>
                <div class="feature-description">
                    Complete content management system for services, projects, and testimonials
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📱</div>
                <div class="feature-title">Mobile API</div>
                <div class="feature-description">
                    RESTful API endpoints for React Native mobile app integration
                </div>
            </div>
        </div>
        
        <div class="status-info">
            <div class="status-title">🚀 Project Status</div>
            <div class="status-grid">
                <div class="status-item completed">
                    <strong>✅ Composer Installed</strong><br>
                    Dependencies loaded
                </div>
                <div class="status-item completed">
                    <strong>✅ Laravel Setup</strong><br>
                    Framework configured
                </div>
                <div class="status-item completed">
                    <strong>✅ Database Schema</strong><br>
                    Models & migrations ready
                </div>
                <div class="status-item pending">
                    <strong>⏳ Database Setup</strong><br>
                    Run migrations next
                </div>
                <div class="status-item pending">
                    <strong>⏳ Controllers</strong><br>
                    API & web controllers
                </div>
                <div class="status-item pending">
                    <strong>⏳ Frontend Views</strong><br>
                    Blade templates
                </div>
            </div>
        </div>
    </div>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\mobile-app-dev\resources\views/welcome.blade.php ENDPATH**/ ?>