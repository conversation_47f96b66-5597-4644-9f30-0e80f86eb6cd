<?php $__env->startSection('title', 'Contact Us'); ?>
<?php $__env->startSection('description', 'Get in touch with Flori Construction Ltd for your construction project. Free consultations and quotes available.'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section style="background: linear-gradient(135deg, #1f2937 0%, #374151 100%); color: white; padding: 4rem 0; text-align: center;">
    <div class="container">
        <h1 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem;">
            Contact Us
        </h1>
        <p style="font-size: 1.125rem; opacity: 0.9; max-width: 600px; margin: 0 auto;">
            Ready to start your construction project? Get in touch with our team for a free consultation and quote.
        </p>
    </div>
</section>

<!-- Contact Section -->
<section style="padding: 4rem 0;">
    <div class="container">
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 4rem; align-items: start;">
            <!-- Contact Form -->
            <div class="card">
                <div style="padding: 2rem;">
                    <h2 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 1rem; color: #1f2937;">
                        Send us a Message
                    </h2>
                    <p style="color: #6b7280; margin-bottom: 2rem;">
                        Fill out the form below and we'll get back to you within 24 hours.
                    </p>

                    <?php if(session('success')): ?>
                        <div class="alert alert-success">
                            <?php echo e(session('success')); ?>

                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="alert alert-error">
                            <?php echo e(session('error')); ?>

                        </div>
                    <?php endif; ?>

                    <form action="<?php echo e(route('contact.store')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                            <div>
                                <label for="name" style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: #374151;">
                                    Full Name *
                                </label>
                                <input 
                                    type="text" 
                                    id="name" 
                                    name="name" 
                                    value="<?php echo e(old('name')); ?>"
                                    required
                                    style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 1rem;"
                                >
                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            
                            <div>
                                <label for="email" style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: #374151;">
                                    Email Address *
                                </label>
                                <input 
                                    type="email" 
                                    id="email" 
                                    name="email" 
                                    value="<?php echo e(old('email')); ?>"
                                    required
                                    style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 1rem;"
                                >
                                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                            <div>
                                <label for="phone" style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: #374151;">
                                    Phone Number
                                </label>
                                <input 
                                    type="tel" 
                                    id="phone" 
                                    name="phone" 
                                    value="<?php echo e(old('phone')); ?>"
                                    style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 1rem;"
                                >
                                <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                            
                            <div>
                                <label for="subject" style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: #374151;">
                                    Subject
                                </label>
                                <select 
                                    id="subject" 
                                    name="subject"
                                    style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 1rem;"
                                >
                                    <option value="">Select a subject</option>
                                    <option value="General Inquiry" <?php echo e(old('subject') == 'General Inquiry' ? 'selected' : ''); ?>>General Inquiry</option>
                                    <option value="Project Quote" <?php echo e(old('subject') == 'Project Quote' ? 'selected' : ''); ?>>Project Quote</option>
                                    <option value="Residential Construction" <?php echo e(old('subject') == 'Residential Construction' ? 'selected' : ''); ?>>Residential Construction</option>
                                    <option value="Commercial Construction" <?php echo e(old('subject') == 'Commercial Construction' ? 'selected' : ''); ?>>Commercial Construction</option>
                                    <option value="Renovation & Remodeling" <?php echo e(old('subject') == 'Renovation & Remodeling' ? 'selected' : ''); ?>>Renovation & Remodeling</option>
                                    <option value="Project Management" <?php echo e(old('subject') == 'Project Management' ? 'selected' : ''); ?>>Project Management</option>
                                </select>
                                <?php $__errorArgs = ['subject'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div style="margin-bottom: 1.5rem;">
                            <label for="message" style="display: block; margin-bottom: 0.5rem; font-weight: 500; color: #374151;">
                                Message *
                            </label>
                            <textarea 
                                id="message" 
                                name="message" 
                                rows="5" 
                                required
                                placeholder="Tell us about your project..."
                                style="width: 100%; padding: 0.75rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 1rem; resize: vertical;"
                            ><?php echo e(old('message')); ?></textarea>
                            <?php $__errorArgs = ['message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <button type="submit" class="btn btn-primary" style="width: 100%; padding: 1rem;">
                            Send Message
                        </button>
                    </form>
                </div>
            </div>

            <!-- Contact Information -->
            <div>
                <h2 style="font-size: 1.5rem; font-weight: 600; margin-bottom: 2rem; color: #1f2937;">
                    Get in Touch
                </h2>

                <!-- Contact Cards -->
                <div style="display: grid; gap: 1.5rem;">
                    <div class="card">
                        <div style="padding: 1.5rem;">
                            <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem;">
                                <div style="background: #2563eb; color: white; width: 3rem; height: 3rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.25rem;">
                                    📞
                                </div>
                                <div>
                                    <h3 style="font-weight: 600; color: #1f2937;">Phone</h3>
                                    <p style="color: #6b7280;">Call us for immediate assistance</p>
                                </div>
                            </div>
                            <p style="font-weight: 600; color: #2563eb;">
                                <a href="tel:******-567-8900" style="text-decoration: none; color: inherit;">******-567-8900</a>
                            </p>
                            <p style="color: #6b7280; font-size: 0.875rem; margin-top: 0.25rem;">
                                Emergency: ******-567-8911
                            </p>
                        </div>
                    </div>

                    <div class="card">
                        <div style="padding: 1.5rem;">
                            <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem;">
                                <div style="background: #2563eb; color: white; width: 3rem; height: 3rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.25rem;">
                                    📧
                                </div>
                                <div>
                                    <h3 style="font-weight: 600; color: #1f2937;">Email</h3>
                                    <p style="color: #6b7280;">Send us an email anytime</p>
                                </div>
                            </div>
                            <p style="font-weight: 600; color: #2563eb;">
                                <a href="mailto:<EMAIL>" style="text-decoration: none; color: inherit;"><EMAIL></a>
                            </p>
                        </div>
                    </div>

                    <div class="card">
                        <div style="padding: 1.5rem;">
                            <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem;">
                                <div style="background: #2563eb; color: white; width: 3rem; height: 3rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.25rem;">
                                    📍
                                </div>
                                <div>
                                    <h3 style="font-weight: 600; color: #1f2937;">Office</h3>
                                    <p style="color: #6b7280;">Visit our office</p>
                                </div>
                            </div>
                            <p style="color: #374151;">
                                123 Construction Ave<br>
                                Building City, BC 12345<br>
                                Canada
                            </p>
                        </div>
                    </div>

                    <div class="card">
                        <div style="padding: 1.5rem;">
                            <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem;">
                                <div style="background: #2563eb; color: white; width: 3rem; height: 3rem; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.25rem;">
                                    🕒
                                </div>
                                <div>
                                    <h3 style="font-weight: 600; color: #1f2937;">Business Hours</h3>
                                    <p style="color: #6b7280;">When we're available</p>
                                </div>
                            </div>
                            <div style="color: #374151; font-size: 0.875rem;">
                                <p><strong>Mon - Fri:</strong> 8:00 AM - 6:00 PM</p>
                                <p><strong>Saturday:</strong> 9:00 AM - 4:00 PM</p>
                                <p><strong>Sunday:</strong> Closed</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\mobile-app-dev\resources\views/contact.blade.php ENDPATH**/ ?>