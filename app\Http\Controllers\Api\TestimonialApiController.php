<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Testimonial;
use Illuminate\Http\Request;

class TestimonialApiController extends Controller
{
    /**
     * Display a listing of testimonials
     */
    public function index()
    {
        try {
            $testimonials = Testimonial::active()
                ->ordered()
                ->get()
                ->map(function ($testimonial) {
                    return [
                        'id' => $testimonial->id,
                        'client_name' => $testimonial->client_name,
                        'client_title' => $testimonial->client_title,
                        'client_company' => $testimonial->client_company,
                        'testimonial' => $testimonial->testimonial,
                        'short_testimonial' => $testimonial->short_testimonial,
                        'rating' => $testimonial->rating,
                        'star_rating' => $testimonial->star_rating,
                        'project_title' => $testimonial->project_title,
                        'is_featured' => $testimonial->is_featured,
                        'client_image_url' => $testimonial->client_image_url,
                        'created_at' => $testimonial->created_at->format('Y-m-d'),
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $testimonials,
                'message' => 'Testimonials retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving testimonials: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display featured testimonials only
     */
    public function featured()
    {
        try {
            $testimonials = Testimonial::active()
                ->featured()
                ->ordered()
                ->get()
                ->map(function ($testimonial) {
                    return [
                        'id' => $testimonial->id,
                        'client_name' => $testimonial->client_name,
                        'client_title' => $testimonial->client_title,
                        'client_company' => $testimonial->client_company,
                        'testimonial' => $testimonial->testimonial,
                        'rating' => $testimonial->rating,
                        'star_rating' => $testimonial->star_rating,
                        'project_title' => $testimonial->project_title,
                        'client_image_url' => $testimonial->client_image_url,
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $testimonials,
                'message' => 'Featured testimonials retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving featured testimonials: ' . $e->getMessage()
            ], 500);
        }
    }
}
