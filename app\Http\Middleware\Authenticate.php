<?php

namespace App\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Http\Request;

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     */
    protected function redirectTo(Request $request): ?string
    {
        if ($request->expectsJson()) {
            return null;
        }

        // Check if this is an admin route
        if ($request->is('admin/*')) {
            return route('admin.login');
        }

        // For regular users, redirect to admin login (since we only have admin auth)
        return route('admin.login');
    }
}
