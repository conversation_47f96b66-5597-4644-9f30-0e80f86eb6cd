@extends('layouts.admin')

@section('title', 'Edit Service')

@section('content')
<div class="flex justify-between items-center mb-4">
    <h1 style="font-size: 1.5rem; font-weight: 600; color: #1f2937;">Edit Service: {{ $service->title }}</h1>
    <div class="flex gap-2">
        <a href="{{ route('services.show', $service->slug) }}" target="_blank" class="btn btn-secondary">
            🔗 View on Website
        </a>
        <a href="{{ route('admin.services.index') }}" class="btn btn-secondary">
            ← Back to Services
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h3>Service Information</h3>
    </div>
    <div class="card-body">
        <form action="{{ route('admin.services.update', $service) }}" method="POST" enctype="multipart/form-data">
            @csrf
            @method('PUT')
            
            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem;">
                <!-- Main Information -->
                <div>
                    <div class="form-group">
                        <label for="title" class="form-label">Service Title *</label>
                        <input 
                            type="text" 
                            id="title" 
                            name="title" 
                            value="{{ old('title', $service->title) }}" 
                            class="form-control" 
                            required
                        >
                        @error('title')
                            <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;">{{ $message }}</p>
                        @enderror
                        <small style="color: #6b7280; font-size: 0.75rem;">Current slug: {{ $service->slug }}</small>
                    </div>

                    <div class="form-group">
                        <label for="description" class="form-label">Short Description *</label>
                        <textarea 
                            id="description" 
                            name="description" 
                            rows="3" 
                            class="form-control" 
                            required
                        >{{ old('description', $service->description) }}</textarea>
                        @error('description')
                            <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="content" class="form-label">Detailed Content</label>
                        <textarea 
                            id="content" 
                            name="content" 
                            rows="8" 
                            class="form-control"
                        >{{ old('content', $service->content) }}</textarea>
                        @error('content')
                            <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;">{{ $message }}</p>
                        @enderror
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label for="price_from" class="form-label">Starting Price ($)</label>
                            <input 
                                type="number" 
                                id="price_from" 
                                name="price_from" 
                                value="{{ old('price_from', $service->price_from) }}" 
                                class="form-control" 
                                min="0" 
                                step="0.01"
                            >
                            @error('price_from')
                                <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="sort_order" class="form-label">Sort Order</label>
                            <input 
                                type="number" 
                                id="sort_order" 
                                name="sort_order" 
                                value="{{ old('sort_order', $service->sort_order) }}" 
                                class="form-control" 
                                min="0"
                            >
                            @error('sort_order')
                                <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Images and Settings -->
                <div>
                    <!-- Current Image -->
                    @if($service->image)
                        <div class="form-group">
                            <label class="form-label">Current Image</label>
                            <div style="margin-bottom: 1rem;">
                                <img src="{{ $service->image_url }}" alt="{{ $service->title }}" 
                                     style="width: 100%; max-width: 200px; height: auto; border-radius: 0.375rem; border: 1px solid #e5e7eb;">
                            </div>
                        </div>
                    @endif

                    <div class="form-group">
                        <label for="image" class="form-label">
                            {{ $service->image ? 'Replace Image' : 'Service Image' }}
                        </label>
                        <input 
                            type="file" 
                            id="image" 
                            name="image" 
                            class="form-control" 
                            accept="image/*"
                        >
                        @error('image')
                            <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;">{{ $message }}</p>
                        @enderror
                        <small style="color: #6b7280; font-size: 0.75rem;">Max 2MB, JPG/PNG/GIF</small>
                    </div>

                    <!-- Current Icon -->
                    @if($service->icon)
                        <div class="form-group">
                            <label class="form-label">Current Icon</label>
                            <div style="margin-bottom: 1rem;">
                                <img src="{{ $service->icon_url }}" alt="{{ $service->title }}" 
                                     style="width: 50px; height: 50px; border-radius: 0.375rem; border: 1px solid #e5e7eb;">
                            </div>
                        </div>
                    @endif

                    <div class="form-group">
                        <label for="icon" class="form-label">
                            {{ $service->icon ? 'Replace Icon' : 'Service Icon' }}
                        </label>
                        <input 
                            type="file" 
                            id="icon" 
                            name="icon" 
                            class="form-control" 
                            accept="image/*"
                        >
                        @error('icon')
                            <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;">{{ $message }}</p>
                        @enderror
                        <small style="color: #6b7280; font-size: 0.75rem;">Max 1MB, JPG/PNG/GIF/SVG</small>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Settings</label>
                        
                        <div style="margin-bottom: 0.5rem;">
                            <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                <input 
                                    type="checkbox" 
                                    name="is_active" 
                                    value="1" 
                                    {{ old('is_active', $service->is_active) ? 'checked' : '' }}
                                >
                                <span>Active (visible on website)</span>
                            </label>
                        </div>
                        
                        <div>
                            <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                <input 
                                    type="checkbox" 
                                    name="is_featured" 
                                    value="1" 
                                    {{ old('is_featured', $service->is_featured) ? 'checked' : '' }}
                                >
                                <span>Featured (show on homepage)</span>
                            </label>
                        </div>
                    </div>

                    <!-- Service Stats -->
                    <div style="background: #f9fafb; padding: 1rem; border-radius: 0.375rem; margin-top: 1rem;">
                        <h4 style="font-size: 0.875rem; font-weight: 600; margin-bottom: 0.5rem; color: #374151;">Service Stats:</h4>
                        <div style="font-size: 0.75rem; color: #6b7280;">
                            <p>Created: {{ $service->created_at->format('M j, Y') }}</p>
                            <p>Last Updated: {{ $service->updated_at->format('M j, Y') }}</p>
                            <p>Status: {{ $service->is_active ? 'Active' : 'Inactive' }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div style="border-top: 1px solid #e5e7eb; margin-top: 2rem; padding-top: 1rem;">
                <div class="flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        💾 Update Service
                    </button>
                    <a href="{{ route('admin.services.index') }}" class="btn btn-secondary">
                        Cancel
                    </a>
                    <form action="{{ route('admin.services.destroy', $service) }}" method="POST" style="display: inline;" 
                          onsubmit="return confirm('Are you sure you want to delete this service? This action cannot be undone.')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">
                            🗑️ Delete Service
                        </button>
                    </form>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection
