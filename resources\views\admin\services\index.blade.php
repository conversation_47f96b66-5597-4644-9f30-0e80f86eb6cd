@extends('layouts.admin')

@section('title', 'Manage Services')

@section('content')
<div class="flex justify-between items-center mb-4">
    <h1 style="font-size: 1.5rem; font-weight: 600; color: #1f2937;">Services Management</h1>
    <a href="{{ route('admin.services.create') }}" class="btn btn-primary">
        ➕ Add New Service
    </a>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ route('admin.services.index') }}" class="flex gap-4 items-end">
            <div style="flex: 1;">
                <label class="form-label">Search</label>
                <input 
                    type="text" 
                    name="search" 
                    value="{{ request('search') }}" 
                    placeholder="Search services..."
                    class="form-control"
                >
            </div>
            
            <div>
                <label class="form-label">Status</label>
                <select name="status" class="form-control">
                    <option value="">All Status</option>
                    <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                    <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                </select>
            </div>
            
            <div>
                <label class="form-label">Featured</label>
                <select name="featured" class="form-control">
                    <option value="">All</option>
                    <option value="1" {{ request('featured') === '1' ? 'selected' : '' }}>Featured Only</option>
                </select>
            </div>
            
            <button type="submit" class="btn btn-secondary">Filter</button>
            <a href="{{ route('admin.services.index') }}" class="btn btn-secondary">Clear</a>
        </form>
    </div>
</div>

<!-- Services Table -->
<div class="card">
    <div class="card-header">
        <h3>Services ({{ $services->total() }} total)</h3>
    </div>
    <div class="card-body" style="padding: 0;">
        @if($services->count() > 0)
            <table class="table">
                <thead>
                    <tr>
                        <th>Image</th>
                        <th>Title</th>
                        <th>Price From</th>
                        <th>Status</th>
                        <th>Featured</th>
                        <th>Sort Order</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($services as $service)
                    <tr>
                        <td>
                            @if($service->image)
                                <img src="{{ $service->image_url }}" alt="{{ $service->title }}" 
                                     style="width: 50px; height: 50px; object-fit: cover; border-radius: 0.375rem;">
                            @else
                                <div style="width: 50px; height: 50px; background: #f3f4f6; border-radius: 0.375rem; display: flex; align-items: center; justify-content: center; font-size: 1.5rem;">
                                    🔧
                                </div>
                            @endif
                        </td>
                        <td>
                            <div>
                                <div style="font-weight: 600; color: #1f2937;">{{ $service->title }}</div>
                                <div style="color: #6b7280; font-size: 0.875rem;">{{ Str::limit($service->description, 50) }}</div>
                            </div>
                        </td>
                        <td>
                            @if($service->price_from)
                                <span style="font-weight: 600; color: #059669;">${{ number_format($service->price_from) }}</span>
                            @else
                                <span style="color: #6b7280;">-</span>
                            @endif
                        </td>
                        <td>
                            @if($service->is_active)
                                <span style="background: #d1fae5; color: #065f46; padding: 0.25rem 0.5rem; border-radius: 9999px; font-size: 0.75rem;">
                                    Active
                                </span>
                            @else
                                <span style="background: #fee2e2; color: #991b1b; padding: 0.25rem 0.5rem; border-radius: 9999px; font-size: 0.75rem;">
                                    Inactive
                                </span>
                            @endif
                        </td>
                        <td>
                            @if($service->is_featured)
                                <span style="background: #dbeafe; color: #1e40af; padding: 0.25rem 0.5rem; border-radius: 9999px; font-size: 0.75rem;">
                                    Featured
                                </span>
                            @else
                                <span style="color: #6b7280;">-</span>
                            @endif
                        </td>
                        <td>
                            <span style="color: #6b7280;">{{ $service->sort_order }}</span>
                        </td>
                        <td>
                            <div class="flex gap-2">
                                <a href="{{ route('admin.services.show', $service) }}" class="btn btn-sm btn-secondary">
                                    👁️ View
                                </a>
                                <a href="{{ route('admin.services.edit', $service) }}" class="btn btn-sm btn-primary">
                                    ✏️ Edit
                                </a>
                                <form action="{{ route('admin.services.destroy', $service) }}" method="POST" style="display: inline;" 
                                      onsubmit="return confirm('Are you sure you want to delete this service?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-danger">
                                        🗑️ Delete
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
            
            <!-- Pagination -->
            @if($services->hasPages())
                <div style="padding: 1rem;">
                    {{ $services->appends(request()->query())->links() }}
                </div>
            @endif
        @else
            <div style="text-align: center; padding: 3rem; color: #6b7280;">
                <div style="font-size: 3rem; margin-bottom: 1rem;">🔧</div>
                <h3 style="margin-bottom: 0.5rem;">No services found</h3>
                <p>Get started by creating your first service.</p>
                <a href="{{ route('admin.services.create') }}" class="btn btn-primary mt-4">
                    ➕ Add New Service
                </a>
            </div>
        @endif
    </div>
</div>
@endsection
