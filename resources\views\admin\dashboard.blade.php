@extends('layouts.admin')

@section('title', 'Dashboard')

@section('content')
<!-- Stats Grid -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-number">{{ $stats['total_services'] }}</div>
        <div class="stat-label">Total Services</div>
        <div style="font-size: 0.75rem; color: #059669; margin-top: 0.25rem;">
            {{ $stats['active_services'] }} active
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-number">{{ $stats['total_projects'] }}</div>
        <div class="stat-label">Total Projects</div>
        <div style="font-size: 0.75rem; color: #059669; margin-top: 0.25rem;">
            {{ $stats['completed_projects'] }} completed
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-number">{{ $stats['total_testimonials'] }}</div>
        <div class="stat-label">Total Testimonials</div>
        <div style="font-size: 0.75rem; color: #059669; margin-top: 0.25rem;">
            {{ $stats['featured_testimonials'] }} featured
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-number">{{ $stats['total_contacts'] }}</div>
        <div class="stat-label">Contact Submissions</div>
        @if($stats['unread_contacts'] > 0)
            <div style="font-size: 0.75rem; color: #dc2626; margin-top: 0.25rem;">
                {{ $stats['unread_contacts'] }} unread
            </div>
        @else
            <div style="font-size: 0.75rem; color: #059669; margin-top: 0.25rem;">
                All read
            </div>
        @endif
    </div>
</div>

<!-- Quick Actions -->
<div class="card mb-4">
    <div class="card-header">
        <h3>Quick Actions</h3>
    </div>
    <div class="card-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
            <a href="{{ route('admin.services.create') }}" class="btn btn-primary">
                ➕ Add New Service
            </a>
            <a href="{{ route('admin.projects.create') }}" class="btn btn-primary">
                ➕ Add New Project
            </a>
            <a href="{{ route('admin.testimonials.create') }}" class="btn btn-primary">
                ➕ Add New Testimonial
            </a>
            <a href="{{ route('admin.contacts') }}" class="btn btn-secondary">
                📧 View Contact Forms
            </a>
        </div>
    </div>
</div>

<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
    <!-- Recent Contact Submissions -->
    <div class="card">
        <div class="card-header">
            <div class="flex justify-between items-center">
                <h3>Recent Contact Submissions</h3>
                <a href="{{ route('admin.contacts') }}" class="btn btn-sm btn-secondary">View All</a>
            </div>
        </div>
        <div class="card-body">
            @if($recentContacts->count() > 0)
                <div style="space-y: 1rem;">
                    @foreach($recentContacts as $contact)
                        <div style="padding: 1rem; border: 1px solid #e5e7eb; border-radius: 0.375rem; margin-bottom: 1rem;">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h4 style="font-weight: 600; color: #1f2937; margin-bottom: 0.25rem;">
                                        {{ $contact->name }}
                                        @if(!$contact->is_read)
                                            <span style="background: #dc2626; color: white; font-size: 0.75rem; padding: 0.125rem 0.375rem; border-radius: 9999px; margin-left: 0.5rem;">New</span>
                                        @endif
                                    </h4>
                                    <p style="color: #6b7280; font-size: 0.875rem; margin-bottom: 0.25rem;">{{ $contact->email }}</p>
                                    <p style="color: #374151; font-size: 0.875rem;">{{ $contact->short_message }}</p>
                                </div>
                                <div style="text-align: right; font-size: 0.75rem; color: #6b7280;">
                                    {{ $contact->created_at->diffForHumans() }}
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <p style="color: #6b7280; text-align: center; padding: 2rem;">No contact submissions yet.</p>
            @endif
        </div>
    </div>

    <!-- Recent Projects -->
    <div class="card">
        <div class="card-header">
            <div class="flex justify-between items-center">
                <h3>Recent Projects</h3>
                <a href="{{ route('admin.projects.index') }}" class="btn btn-sm btn-secondary">View All</a>
            </div>
        </div>
        <div class="card-body">
            @if($recentProjects->count() > 0)
                <div style="space-y: 1rem;">
                    @foreach($recentProjects as $project)
                        <div style="padding: 1rem; border: 1px solid #e5e7eb; border-radius: 0.375rem; margin-bottom: 1rem;">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h4 style="font-weight: 600; color: #1f2937; margin-bottom: 0.25rem;">
                                        {{ $project->title }}
                                        @if($project->is_featured)
                                            <span style="background: #059669; color: white; font-size: 0.75rem; padding: 0.125rem 0.375rem; border-radius: 9999px; margin-left: 0.5rem;">Featured</span>
                                        @endif
                                    </h4>
                                    <p style="color: #6b7280; font-size: 0.875rem; margin-bottom: 0.25rem;">{{ $project->client_name }}</p>
                                    <p style="color: #374151; font-size: 0.875rem;">{{ $project->location }}</p>
                                    @if($project->project_value)
                                        <p style="color: #059669; font-weight: 600; font-size: 0.875rem; margin-top: 0.25rem;">
                                            ${{ number_format($project->project_value) }}
                                        </p>
                                    @endif
                                </div>
                                <div style="text-align: right; font-size: 0.75rem; color: #6b7280;">
                                    <div>{{ $project->status }}</div>
                                    <div>{{ $project->created_at->diffForHumans() }}</div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <p style="color: #6b7280; text-align: center; padding: 2rem;">No projects yet.</p>
            @endif
        </div>
    </div>
</div>

<!-- System Information -->
<div class="card mt-4">
    <div class="card-header">
        <h3>System Information</h3>
    </div>
    <div class="card-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
            <div>
                <strong>Laravel Version:</strong><br>
                <span style="color: #6b7280;">{{ app()->version() }}</span>
            </div>
            <div>
                <strong>PHP Version:</strong><br>
                <span style="color: #6b7280;">{{ PHP_VERSION }}</span>
            </div>
            <div>
                <strong>Environment:</strong><br>
                <span style="color: #6b7280;">{{ app()->environment() }}</span>
            </div>
            <div>
                <strong>Last Login:</strong><br>
                <span style="color: #6b7280;">
                    @if(Auth::guard('admin')->user()->last_login_at)
                        {{ Auth::guard('admin')->user()->last_login_at->diffForHumans() }}
                    @else
                        First time login
                    @endif
                </span>
            </div>
        </div>
    </div>
</div>
@endsection
