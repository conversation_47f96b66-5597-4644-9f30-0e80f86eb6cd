<?php $__env->startSection('title', 'Manage Services'); ?>

<?php $__env->startSection('content'); ?>
<div class="flex justify-between items-center mb-4">
    <h1 style="font-size: 1.5rem; font-weight: 600; color: #1f2937;">Services Management</h1>
    <a href="<?php echo e(route('admin.services.create')); ?>" class="btn btn-primary">
        ➕ Add New Service
    </a>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?php echo e(route('admin.services.index')); ?>" class="flex gap-4 items-end">
            <div style="flex: 1;">
                <label class="form-label">Search</label>
                <input 
                    type="text" 
                    name="search" 
                    value="<?php echo e(request('search')); ?>" 
                    placeholder="Search services..."
                    class="form-control"
                >
            </div>
            
            <div>
                <label class="form-label">Status</label>
                <select name="status" class="form-control">
                    <option value="">All Status</option>
                    <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                    <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                </select>
            </div>
            
            <div>
                <label class="form-label">Featured</label>
                <select name="featured" class="form-control">
                    <option value="">All</option>
                    <option value="1" <?php echo e(request('featured') === '1' ? 'selected' : ''); ?>>Featured Only</option>
                </select>
            </div>
            
            <button type="submit" class="btn btn-secondary">Filter</button>
            <a href="<?php echo e(route('admin.services.index')); ?>" class="btn btn-secondary">Clear</a>
        </form>
    </div>
</div>

<!-- Services Table -->
<div class="card">
    <div class="card-header">
        <h3>Services (<?php echo e($services->total()); ?> total)</h3>
    </div>
    <div class="card-body" style="padding: 0;">
        <?php if($services->count() > 0): ?>
            <table class="table">
                <thead>
                    <tr>
                        <th>Image</th>
                        <th>Title</th>
                        <th>Price From</th>
                        <th>Status</th>
                        <th>Featured</th>
                        <th>Sort Order</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td>
                            <?php if($service->image): ?>
                                <img src="<?php echo e($service->image_url); ?>" alt="<?php echo e($service->title); ?>" 
                                     style="width: 50px; height: 50px; object-fit: cover; border-radius: 0.375rem;">
                            <?php else: ?>
                                <div style="width: 50px; height: 50px; background: #f3f4f6; border-radius: 0.375rem; display: flex; align-items: center; justify-content: center; font-size: 1.5rem;">
                                    🔧
                                </div>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div>
                                <div style="font-weight: 600; color: #1f2937;"><?php echo e($service->title); ?></div>
                                <div style="color: #6b7280; font-size: 0.875rem;"><?php echo e(Str::limit($service->description, 50)); ?></div>
                            </div>
                        </td>
                        <td>
                            <?php if($service->price_from): ?>
                                <span style="font-weight: 600; color: #059669;">$<?php echo e(number_format($service->price_from)); ?></span>
                            <?php else: ?>
                                <span style="color: #6b7280;">-</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if($service->is_active): ?>
                                <span style="background: #d1fae5; color: #065f46; padding: 0.25rem 0.5rem; border-radius: 9999px; font-size: 0.75rem;">
                                    Active
                                </span>
                            <?php else: ?>
                                <span style="background: #fee2e2; color: #991b1b; padding: 0.25rem 0.5rem; border-radius: 9999px; font-size: 0.75rem;">
                                    Inactive
                                </span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if($service->is_featured): ?>
                                <span style="background: #dbeafe; color: #1e40af; padding: 0.25rem 0.5rem; border-radius: 9999px; font-size: 0.75rem;">
                                    Featured
                                </span>
                            <?php else: ?>
                                <span style="color: #6b7280;">-</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span style="color: #6b7280;"><?php echo e($service->sort_order); ?></span>
                        </td>
                        <td>
                            <div class="flex gap-2">
                                <a href="<?php echo e(route('admin.services.show', $service)); ?>" class="btn btn-sm btn-secondary">
                                    👁️ View
                                </a>
                                <a href="<?php echo e(route('admin.services.edit', $service)); ?>" class="btn btn-sm btn-primary">
                                    ✏️ Edit
                                </a>
                                <form action="<?php echo e(route('admin.services.destroy', $service)); ?>" method="POST" style="display: inline;" 
                                      onsubmit="return confirm('Are you sure you want to delete this service?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="btn btn-sm btn-danger">
                                        🗑️ Delete
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
            
            <!-- Pagination -->
            <?php if($services->hasPages()): ?>
                <div style="padding: 1rem;">
                    <?php echo e($services->appends(request()->query())->links()); ?>

                </div>
            <?php endif; ?>
        <?php else: ?>
            <div style="text-align: center; padding: 3rem; color: #6b7280;">
                <div style="font-size: 3rem; margin-bottom: 1rem;">🔧</div>
                <h3 style="margin-bottom: 0.5rem;">No services found</h3>
                <p>Get started by creating your first service.</p>
                <a href="<?php echo e(route('admin.services.create')); ?>" class="btn btn-primary mt-4">
                    ➕ Add New Service
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\mobile-app-dev\resources\views/admin/services/index.blade.php ENDPATH**/ ?>