<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class AdminServiceController extends Controller
{
    /**
     * Display a listing of services
     */
    public function index(Request $request)
    {
        $query = Service::query();

        // Search
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->has('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Filter by featured
        if ($request->has('featured') && $request->featured === '1') {
            $query->where('is_featured', true);
        }

        $services = $query->orderBy('sort_order', 'asc')
                         ->orderBy('created_at', 'desc')
                         ->paginate(20);

        return view('admin.services.index', compact('services'));
    }

    /**
     * Show the form for creating a new service
     */
    public function create()
    {
        return view('admin.services.create');
    }

    /**
     * Store a newly created service
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:500',
            'content' => 'nullable|string',
            'price_from' => 'nullable|numeric|min:0',
            'sort_order' => 'nullable|integer|min:0',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'icon' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:1024',
        ]);

        $data = $request->only([
            'title', 'description', 'content', 'price_from', 'sort_order'
        ]);

        // Generate slug
        $data['slug'] = Str::slug($request->title);
        
        // Ensure unique slug
        $originalSlug = $data['slug'];
        $counter = 1;
        while (Service::where('slug', $data['slug'])->exists()) {
            $data['slug'] = $originalSlug . '-' . $counter;
            $counter++;
        }

        $data['is_featured'] = $request->has('is_featured');
        $data['is_active'] = $request->has('is_active');

        // Handle image upload
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('services', 'public');
            $data['image'] = basename($imagePath);
        }

        // Handle icon upload
        if ($request->hasFile('icon')) {
            $iconPath = $request->file('icon')->store('services/icons', 'public');
            $data['icon'] = basename($iconPath);
        }

        Service::create($data);

        return redirect()->route('admin.services.index')
                        ->with('success', 'Service created successfully!');
    }

    /**
     * Display the specified service
     */
    public function show(Service $service)
    {
        return view('admin.services.show', compact('service'));
    }

    /**
     * Show the form for editing the specified service
     */
    public function edit(Service $service)
    {
        return view('admin.services.edit', compact('service'));
    }

    /**
     * Update the specified service
     */
    public function update(Request $request, Service $service)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:500',
            'content' => 'nullable|string',
            'price_from' => 'nullable|numeric|min:0',
            'sort_order' => 'nullable|integer|min:0',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'icon' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:1024',
        ]);

        $data = $request->only([
            'title', 'description', 'content', 'price_from', 'sort_order'
        ]);

        // Update slug if title changed
        if ($service->title !== $request->title) {
            $data['slug'] = Str::slug($request->title);
            
            // Ensure unique slug
            $originalSlug = $data['slug'];
            $counter = 1;
            while (Service::where('slug', $data['slug'])->where('id', '!=', $service->id)->exists()) {
                $data['slug'] = $originalSlug . '-' . $counter;
                $counter++;
            }
        }

        $data['is_featured'] = $request->has('is_featured');
        $data['is_active'] = $request->has('is_active');

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($service->image) {
                Storage::disk('public')->delete('services/' . $service->image);
            }
            
            $imagePath = $request->file('image')->store('services', 'public');
            $data['image'] = basename($imagePath);
        }

        // Handle icon upload
        if ($request->hasFile('icon')) {
            // Delete old icon
            if ($service->icon) {
                Storage::disk('public')->delete('services/icons/' . $service->icon);
            }
            
            $iconPath = $request->file('icon')->store('services/icons', 'public');
            $data['icon'] = basename($iconPath);
        }

        $service->update($data);

        return redirect()->route('admin.services.index')
                        ->with('success', 'Service updated successfully!');
    }

    /**
     * Remove the specified service
     */
    public function destroy(Service $service)
    {
        // Delete associated files
        if ($service->image) {
            Storage::disk('public')->delete('services/' . $service->image);
        }
        
        if ($service->icon) {
            Storage::disk('public')->delete('services/icons/' . $service->icon);
        }

        $service->delete();

        return redirect()->route('admin.services.index')
                        ->with('success', 'Service deleted successfully!');
    }
}
