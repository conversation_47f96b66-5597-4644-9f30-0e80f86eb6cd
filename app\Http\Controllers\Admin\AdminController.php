<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AdminUser;
use App\Models\Service;
use App\Models\Project;
use App\Models\Testimonial;
use App\Models\ContactSubmission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class AdminController extends Controller
{
    /**
     * Show the admin login form
     */
    public function showLogin()
    {
        if (Auth::guard('admin')->check()) {
            return redirect()->route('admin.dashboard');
        }

        return view('admin.login');
    }

    /**
     * Handle admin login
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        $credentials = $request->only('email', 'password');

        if (Auth::guard('admin')->attempt($credentials, $request->filled('remember'))) {
            $admin = Auth::guard('admin')->user();
            $admin->updateLastLogin();

            return redirect()->intended(route('admin.dashboard'))
                ->with('success', 'Welcome back, ' . $admin->name . '!');
        }

        return back()
            ->withErrors(['email' => 'Invalid credentials'])
            ->withInput($request->only('email'));
    }

    /**
     * Handle admin logout
     */
    public function logout(Request $request)
    {
        Auth::guard('admin')->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('admin.login')
            ->with('success', 'You have been logged out successfully.');
    }

    /**
     * Show the admin dashboard
     */
    public function dashboard()
    {
        $stats = [
            'total_services' => Service::count(),
            'active_services' => Service::active()->count(),
            'total_projects' => Project::count(),
            'completed_projects' => Project::where('status', 'completed')->count(),
            'total_testimonials' => Testimonial::count(),
            'featured_testimonials' => Testimonial::featured()->count(),
            'total_contacts' => ContactSubmission::count(),
            'unread_contacts' => ContactSubmission::unread()->count(),
        ];

        $recentContacts = ContactSubmission::orderBy('created_at', 'desc')->take(5)->get();
        $recentProjects = Project::orderBy('created_at', 'desc')->take(5)->get();

        return view('admin.dashboard', compact('stats', 'recentContacts', 'recentProjects'));
    }

    /**
     * Show contact form submissions
     */
    public function contacts(Request $request)
    {
        $query = ContactSubmission::query();

        // Filter by read status
        if ($request->has('status')) {
            if ($request->status === 'unread') {
                $query->unread();
            } elseif ($request->status === 'read') {
                $query->read();
            }
        }

        // Filter by source
        if ($request->has('source') && $request->source !== 'all') {
            if ($request->source === 'website') {
                $query->fromWebsite();
            } elseif ($request->source === 'mobile') {
                $query->fromMobileApp();
            }
        }

        // Search
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%")
                  ->orWhere('message', 'like', "%{$search}%");
            });
        }

        $contacts = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('admin.contacts', compact('contacts'));
    }
}
