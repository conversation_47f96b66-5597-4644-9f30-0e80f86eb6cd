@extends('layouts.app')

@section('title', 'Professional Construction Services')
@section('description', 'Flori Construction Ltd provides professional construction services including residential, commercial construction, renovations, and project management in British Columbia.')

@section('content')
<!-- Hero Section -->
<section style="background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%); color: white; padding: 6rem 0; text-align: center;">
    <div class="container">
        <h1 style="font-size: 3rem; font-weight: 700; margin-bottom: 1rem;">
            Building Your Dreams with Excellence
        </h1>
        <p style="font-size: 1.25rem; margin-bottom: 2rem; opacity: 0.9;">
            Professional construction services for residential and commercial projects across British Columbia
        </p>
        <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
            <a href="{{ route('services') }}" class="btn btn-primary" style="background: white; color: #2563eb;">
                Our Services
            </a>
            <a href="{{ route('contact') }}" class="btn btn-secondary" style="background: transparent; border: 2px solid white;">
                Get Quote
            </a>
        </div>
    </div>
</section>

<!-- Featured Services Section -->
<section style="padding: 4rem 0; background: #f9fafb;">
    <div class="container">
        <div class="text-center mb-8">
            <h2 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem; color: #1f2937;">
                Our Services
            </h2>
            <p style="font-size: 1.125rem; color: #6b7280; max-width: 600px; margin: 0 auto;">
                We provide comprehensive construction services with a focus on quality, safety, and customer satisfaction.
            </p>
        </div>
        
        <div class="grid grid-cols-3">
            @foreach($featuredServices as $service)
            <div class="card">
                @if($service->image)
                    <img src="{{ $service->image_url }}" alt="{{ $service->title }}" style="width: 100%; height: 200px; object-fit: cover;">
                @else
                    <div style="width: 100%; height: 200px; background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%); display: flex; align-items: center; justify-content: center; font-size: 3rem;">
                        🏗️
                    </div>
                @endif
                
                <div class="p-6">
                    <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 0.5rem; color: #1f2937;">
                        {{ $service->title }}
                    </h3>
                    <p style="color: #6b7280; margin-bottom: 1rem;">
                        {{ $service->description }}
                    </p>
                    @if($service->price_from)
                        <p style="font-weight: 600; color: #2563eb; margin-bottom: 1rem;">
                            Starting from ${{ number_format($service->price_from) }}
                        </p>
                    @endif
                    <a href="{{ route('services.show', $service->slug) }}" class="btn btn-primary" style="width: 100%;">
                        Learn More
                    </a>
                </div>
            </div>
            @endforeach
        </div>
        
        <div class="text-center mt-8">
            <a href="{{ route('services') }}" class="btn btn-secondary">
                View All Services
            </a>
        </div>
    </div>
</section>

<!-- Featured Projects Section -->
<section style="padding: 4rem 0;">
    <div class="container">
        <div class="text-center mb-8">
            <h2 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem; color: #1f2937;">
                Recent Projects
            </h2>
            <p style="font-size: 1.125rem; color: #6b7280; max-width: 600px; margin: 0 auto;">
                Take a look at some of our recently completed construction projects.
            </p>
        </div>
        
        <div class="grid grid-cols-3">
            @foreach($featuredProjects as $project)
            <div class="card">
                @if($project->featured_image)
                    <img src="{{ $project->featured_image_url }}" alt="{{ $project->title }}" style="width: 100%; height: 200px; object-fit: cover;">
                @else
                    <div style="width: 100%; height: 200px; background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%); display: flex; align-items: center; justify-content: center; font-size: 3rem;">
                        🏢
                    </div>
                @endif
                
                <div class="p-6">
                    <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 0.5rem; color: #1f2937;">
                        {{ $project->title }}
                    </h3>
                    <p style="color: #6b7280; margin-bottom: 1rem;">
                        {{ $project->description }}
                    </p>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                        @if($project->location)
                            <span style="color: #6b7280; font-size: 0.875rem;">📍 {{ $project->location }}</span>
                        @endif
                        @if($project->project_value)
                            <span style="font-weight: 600; color: #059669;">${{ number_format($project->project_value) }}</span>
                        @endif
                    </div>
                    <a href="{{ route('projects.show', $project->slug) }}" class="btn btn-primary" style="width: 100%;">
                        View Project
                    </a>
                </div>
            </div>
            @endforeach
        </div>
        
        <div class="text-center mt-8">
            <a href="{{ route('projects') }}" class="btn btn-secondary">
                View All Projects
            </a>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
@if($featuredTestimonials->count() > 0)
<section style="padding: 4rem 0; background: #f9fafb;">
    <div class="container">
        <div class="text-center mb-8">
            <h2 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem; color: #1f2937;">
                What Our Clients Say
            </h2>
            <p style="font-size: 1.125rem; color: #6b7280;">
                Don't just take our word for it - hear from our satisfied clients.
            </p>
        </div>
        
        <div class="grid grid-cols-3">
            @foreach($featuredTestimonials as $testimonial)
            <div class="card">
                <div class="p-6">
                    <div style="color: #fbbf24; margin-bottom: 1rem; font-size: 1.25rem;">
                        {{ $testimonial->star_rating }}
                    </div>
                    <p style="color: #374151; margin-bottom: 1rem; font-style: italic;">
                        "{{ $testimonial->testimonial }}"
                    </p>
                    <div>
                        <p style="font-weight: 600; color: #1f2937;">{{ $testimonial->client_name }}</p>
                        @if($testimonial->client_title || $testimonial->client_company)
                            <p style="color: #6b7280; font-size: 0.875rem;">
                                @if($testimonial->client_title){{ $testimonial->client_title }}@endif
                                @if($testimonial->client_title && $testimonial->client_company), @endif
                                @if($testimonial->client_company){{ $testimonial->client_company }}@endif
                            </p>
                        @endif
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Call to Action Section -->
<section style="padding: 4rem 0; background: #1f2937; color: white; text-align: center;">
    <div class="container">
        <h2 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem;">
            Ready to Start Your Project?
        </h2>
        <p style="font-size: 1.125rem; margin-bottom: 2rem; opacity: 0.9;">
            Contact us today for a free consultation and quote for your construction project.
        </p>
        <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
            <a href="{{ route('contact') }}" class="btn btn-primary">
                Get Free Quote
            </a>
            <a href="tel:******-567-8900" class="btn btn-secondary" style="background: transparent; border: 2px solid white;">
                📞 Call Now
            </a>
        </div>
    </div>
</section>
@endsection
