@if ($paginator->hasPages())
    <nav style="display: flex; justify-content: center; margin-top: 1rem;">
        <ul style="display: flex; list-style: none; gap: 0.5rem; margin: 0; padding: 0;">
            {{-- Previous Page Link --}}
            @if ($paginator->onFirstPage())
                <li>
                    <span style="padding: 0.5rem 0.75rem; background: #f3f4f6; color: #9ca3af; border-radius: 0.375rem; cursor: not-allowed;">
                        ‹ Previous
                    </span>
                </li>
            @else
                <li>
                    <a href="{{ $paginator->previousPageUrl() }}" 
                       style="padding: 0.5rem 0.75rem; background: #2563eb; color: white; text-decoration: none; border-radius: 0.375rem; transition: background 0.3s;"
                       onmouseover="this.style.background='#1d4ed8'" 
                       onmouseout="this.style.background='#2563eb'">
                        ‹ Previous
                    </a>
                </li>
            @endif

            {{-- Pagination Elements --}}
            @foreach ($elements as $element)
                {{-- "Three Dots" Separator --}}
                @if (is_string($element))
                    <li>
                        <span style="padding: 0.5rem 0.75rem; color: #6b7280;">{{ $element }}</span>
                    </li>
                @endif

                {{-- Array Of Links --}}
                @if (is_array($element))
                    @foreach ($element as $page => $url)
                        @if ($page == $paginator->currentPage())
                            <li>
                                <span style="padding: 0.5rem 0.75rem; background: #2563eb; color: white; border-radius: 0.375rem; font-weight: 600;">
                                    {{ $page }}
                                </span>
                            </li>
                        @else
                            <li>
                                <a href="{{ $url }}" 
                                   style="padding: 0.5rem 0.75rem; background: #f9fafb; color: #374151; text-decoration: none; border-radius: 0.375rem; border: 1px solid #e5e7eb; transition: all 0.3s;"
                                   onmouseover="this.style.background='#f3f4f6'" 
                                   onmouseout="this.style.background='#f9fafb'">
                                    {{ $page }}
                                </a>
                            </li>
                        @endif
                    @endforeach
                @endif
            @endforeach

            {{-- Next Page Link --}}
            @if ($paginator->hasMorePages())
                <li>
                    <a href="{{ $paginator->nextPageUrl() }}" 
                       style="padding: 0.5rem 0.75rem; background: #2563eb; color: white; text-decoration: none; border-radius: 0.375rem; transition: background 0.3s;"
                       onmouseover="this.style.background='#1d4ed8'" 
                       onmouseout="this.style.background='#2563eb'">
                        Next ›
                    </a>
                </li>
            @else
                <li>
                    <span style="padding: 0.5rem 0.75rem; background: #f3f4f6; color: #9ca3af; border-radius: 0.375rem; cursor: not-allowed;">
                        Next ›
                    </span>
                </li>
            @endif
        </ul>
    </nav>
@endif
