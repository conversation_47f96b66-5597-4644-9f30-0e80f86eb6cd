<?php $__env->startSection('title', 'Professional Construction Services'); ?>
<?php $__env->startSection('description', 'Flori Construction Ltd provides professional construction services including residential, commercial construction, renovations, and project management in British Columbia.'); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section style="background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%); color: white; padding: 6rem 0; text-align: center;">
    <div class="container">
        <h1 style="font-size: 3rem; font-weight: 700; margin-bottom: 1rem;">
            Building Your Dreams with Excellence
        </h1>
        <p style="font-size: 1.25rem; margin-bottom: 2rem; opacity: 0.9;">
            Professional construction services for residential and commercial projects across British Columbia
        </p>
        <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
            <a href="<?php echo e(route('services')); ?>" class="btn btn-primary" style="background: white; color: #2563eb;">
                Our Services
            </a>
            <a href="<?php echo e(route('contact')); ?>" class="btn btn-secondary" style="background: transparent; border: 2px solid white;">
                Get Quote
            </a>
        </div>
    </div>
</section>

<!-- Featured Services Section -->
<section style="padding: 4rem 0; background: #f9fafb;">
    <div class="container">
        <div class="text-center mb-8">
            <h2 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem; color: #1f2937;">
                Our Services
            </h2>
            <p style="font-size: 1.125rem; color: #6b7280; max-width: 600px; margin: 0 auto;">
                We provide comprehensive construction services with a focus on quality, safety, and customer satisfaction.
            </p>
        </div>
        
        <div class="grid grid-cols-3">
            <?php $__currentLoopData = $featuredServices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="card">
                <?php if($service->image): ?>
                    <img src="<?php echo e($service->image_url); ?>" alt="<?php echo e($service->title); ?>" style="width: 100%; height: 200px; object-fit: cover;">
                <?php else: ?>
                    <div style="width: 100%; height: 200px; background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%); display: flex; align-items: center; justify-content: center; font-size: 3rem;">
                        🏗️
                    </div>
                <?php endif; ?>
                
                <div class="p-6">
                    <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 0.5rem; color: #1f2937;">
                        <?php echo e($service->title); ?>

                    </h3>
                    <p style="color: #6b7280; margin-bottom: 1rem;">
                        <?php echo e($service->description); ?>

                    </p>
                    <?php if($service->price_from): ?>
                        <p style="font-weight: 600; color: #2563eb; margin-bottom: 1rem;">
                            Starting from $<?php echo e(number_format($service->price_from)); ?>

                        </p>
                    <?php endif; ?>
                    <a href="<?php echo e(route('services.show', $service->slug)); ?>" class="btn btn-primary" style="width: 100%;">
                        Learn More
                    </a>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        
        <div class="text-center mt-8">
            <a href="<?php echo e(route('services')); ?>" class="btn btn-secondary">
                View All Services
            </a>
        </div>
    </div>
</section>

<!-- Featured Projects Section -->
<section style="padding: 4rem 0;">
    <div class="container">
        <div class="text-center mb-8">
            <h2 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem; color: #1f2937;">
                Recent Projects
            </h2>
            <p style="font-size: 1.125rem; color: #6b7280; max-width: 600px; margin: 0 auto;">
                Take a look at some of our recently completed construction projects.
            </p>
        </div>
        
        <div class="grid grid-cols-3">
            <?php $__currentLoopData = $featuredProjects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="card">
                <?php if($project->featured_image): ?>
                    <img src="<?php echo e($project->featured_image_url); ?>" alt="<?php echo e($project->title); ?>" style="width: 100%; height: 200px; object-fit: cover;">
                <?php else: ?>
                    <div style="width: 100%; height: 200px; background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%); display: flex; align-items: center; justify-content: center; font-size: 3rem;">
                        🏢
                    </div>
                <?php endif; ?>
                
                <div class="p-6">
                    <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 0.5rem; color: #1f2937;">
                        <?php echo e($project->title); ?>

                    </h3>
                    <p style="color: #6b7280; margin-bottom: 1rem;">
                        <?php echo e($project->description); ?>

                    </p>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                        <?php if($project->location): ?>
                            <span style="color: #6b7280; font-size: 0.875rem;">📍 <?php echo e($project->location); ?></span>
                        <?php endif; ?>
                        <?php if($project->project_value): ?>
                            <span style="font-weight: 600; color: #059669;">$<?php echo e(number_format($project->project_value)); ?></span>
                        <?php endif; ?>
                    </div>
                    <a href="<?php echo e(route('projects.show', $project->slug)); ?>" class="btn btn-primary" style="width: 100%;">
                        View Project
                    </a>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        
        <div class="text-center mt-8">
            <a href="<?php echo e(route('projects')); ?>" class="btn btn-secondary">
                View All Projects
            </a>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<?php if($featuredTestimonials->count() > 0): ?>
<section style="padding: 4rem 0; background: #f9fafb;">
    <div class="container">
        <div class="text-center mb-8">
            <h2 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem; color: #1f2937;">
                What Our Clients Say
            </h2>
            <p style="font-size: 1.125rem; color: #6b7280;">
                Don't just take our word for it - hear from our satisfied clients.
            </p>
        </div>
        
        <div class="grid grid-cols-3">
            <?php $__currentLoopData = $featuredTestimonials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="card">
                <div class="p-6">
                    <div style="color: #fbbf24; margin-bottom: 1rem; font-size: 1.25rem;">
                        <?php echo e($testimonial->star_rating); ?>

                    </div>
                    <p style="color: #374151; margin-bottom: 1rem; font-style: italic;">
                        "<?php echo e($testimonial->testimonial); ?>"
                    </p>
                    <div>
                        <p style="font-weight: 600; color: #1f2937;"><?php echo e($testimonial->client_name); ?></p>
                        <?php if($testimonial->client_title || $testimonial->client_company): ?>
                            <p style="color: #6b7280; font-size: 0.875rem;">
                                <?php if($testimonial->client_title): ?><?php echo e($testimonial->client_title); ?><?php endif; ?>
                                <?php if($testimonial->client_title && $testimonial->client_company): ?>, <?php endif; ?>
                                <?php if($testimonial->client_company): ?><?php echo e($testimonial->client_company); ?><?php endif; ?>
                            </p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Call to Action Section -->
<section style="padding: 4rem 0; background: #1f2937; color: white; text-align: center;">
    <div class="container">
        <h2 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem;">
            Ready to Start Your Project?
        </h2>
        <p style="font-size: 1.125rem; margin-bottom: 2rem; opacity: 0.9;">
            Contact us today for a free consultation and quote for your construction project.
        </p>
        <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
            <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary">
                Get Free Quote
            </a>
            <a href="tel:******-567-8900" class="btn btn-secondary" style="background: transparent; border: 2px solid white;">
                📞 Call Now
            </a>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\mobile-app-dev\resources\views/home.blade.php ENDPATH**/ ?>