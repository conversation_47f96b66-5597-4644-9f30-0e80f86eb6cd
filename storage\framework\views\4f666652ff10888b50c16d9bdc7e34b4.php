<?php $__env->startSection('title', 'Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<!-- Stats Grid -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-number"><?php echo e($stats['total_services']); ?></div>
        <div class="stat-label">Total Services</div>
        <div style="font-size: 0.75rem; color: #059669; margin-top: 0.25rem;">
            <?php echo e($stats['active_services']); ?> active
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-number"><?php echo e($stats['total_projects']); ?></div>
        <div class="stat-label">Total Projects</div>
        <div style="font-size: 0.75rem; color: #059669; margin-top: 0.25rem;">
            <?php echo e($stats['completed_projects']); ?> completed
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-number"><?php echo e($stats['total_testimonials']); ?></div>
        <div class="stat-label">Total Testimonials</div>
        <div style="font-size: 0.75rem; color: #059669; margin-top: 0.25rem;">
            <?php echo e($stats['featured_testimonials']); ?> featured
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-number"><?php echo e($stats['total_contacts']); ?></div>
        <div class="stat-label">Contact Submissions</div>
        <?php if($stats['unread_contacts'] > 0): ?>
            <div style="font-size: 0.75rem; color: #dc2626; margin-top: 0.25rem;">
                <?php echo e($stats['unread_contacts']); ?> unread
            </div>
        <?php else: ?>
            <div style="font-size: 0.75rem; color: #059669; margin-top: 0.25rem;">
                All read
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Quick Actions -->
<div class="card mb-4">
    <div class="card-header">
        <h3>Quick Actions</h3>
    </div>
    <div class="card-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
            <a href="<?php echo e(route('admin.services.create')); ?>" class="btn btn-primary">
                ➕ Add New Service
            </a>
            <a href="<?php echo e(route('admin.projects.create')); ?>" class="btn btn-primary">
                ➕ Add New Project
            </a>
            <a href="<?php echo e(route('admin.testimonials.create')); ?>" class="btn btn-primary">
                ➕ Add New Testimonial
            </a>
            <a href="<?php echo e(route('admin.contacts')); ?>" class="btn btn-secondary">
                📧 View Contact Forms
            </a>
        </div>
    </div>
</div>

<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
    <!-- Recent Contact Submissions -->
    <div class="card">
        <div class="card-header">
            <div class="flex justify-between items-center">
                <h3>Recent Contact Submissions</h3>
                <a href="<?php echo e(route('admin.contacts')); ?>" class="btn btn-sm btn-secondary">View All</a>
            </div>
        </div>
        <div class="card-body">
            <?php if($recentContacts->count() > 0): ?>
                <div style="space-y: 1rem;">
                    <?php $__currentLoopData = $recentContacts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contact): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div style="padding: 1rem; border: 1px solid #e5e7eb; border-radius: 0.375rem; margin-bottom: 1rem;">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h4 style="font-weight: 600; color: #1f2937; margin-bottom: 0.25rem;">
                                        <?php echo e($contact->name); ?>

                                        <?php if(!$contact->is_read): ?>
                                            <span style="background: #dc2626; color: white; font-size: 0.75rem; padding: 0.125rem 0.375rem; border-radius: 9999px; margin-left: 0.5rem;">New</span>
                                        <?php endif; ?>
                                    </h4>
                                    <p style="color: #6b7280; font-size: 0.875rem; margin-bottom: 0.25rem;"><?php echo e($contact->email); ?></p>
                                    <p style="color: #374151; font-size: 0.875rem;"><?php echo e($contact->short_message); ?></p>
                                </div>
                                <div style="text-align: right; font-size: 0.75rem; color: #6b7280;">
                                    <?php echo e($contact->created_at->diffForHumans()); ?>

                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <p style="color: #6b7280; text-align: center; padding: 2rem;">No contact submissions yet.</p>
            <?php endif; ?>
        </div>
    </div>

    <!-- Recent Projects -->
    <div class="card">
        <div class="card-header">
            <div class="flex justify-between items-center">
                <h3>Recent Projects</h3>
                <a href="<?php echo e(route('admin.projects.index')); ?>" class="btn btn-sm btn-secondary">View All</a>
            </div>
        </div>
        <div class="card-body">
            <?php if($recentProjects->count() > 0): ?>
                <div style="space-y: 1rem;">
                    <?php $__currentLoopData = $recentProjects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div style="padding: 1rem; border: 1px solid #e5e7eb; border-radius: 0.375rem; margin-bottom: 1rem;">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h4 style="font-weight: 600; color: #1f2937; margin-bottom: 0.25rem;">
                                        <?php echo e($project->title); ?>

                                        <?php if($project->is_featured): ?>
                                            <span style="background: #059669; color: white; font-size: 0.75rem; padding: 0.125rem 0.375rem; border-radius: 9999px; margin-left: 0.5rem;">Featured</span>
                                        <?php endif; ?>
                                    </h4>
                                    <p style="color: #6b7280; font-size: 0.875rem; margin-bottom: 0.25rem;"><?php echo e($project->client_name); ?></p>
                                    <p style="color: #374151; font-size: 0.875rem;"><?php echo e($project->location); ?></p>
                                    <?php if($project->project_value): ?>
                                        <p style="color: #059669; font-weight: 600; font-size: 0.875rem; margin-top: 0.25rem;">
                                            $<?php echo e(number_format($project->project_value)); ?>

                                        </p>
                                    <?php endif; ?>
                                </div>
                                <div style="text-align: right; font-size: 0.75rem; color: #6b7280;">
                                    <div><?php echo e($project->status); ?></div>
                                    <div><?php echo e($project->created_at->diffForHumans()); ?></div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <p style="color: #6b7280; text-align: center; padding: 2rem;">No projects yet.</p>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="card mt-4">
    <div class="card-header">
        <h3>System Information</h3>
    </div>
    <div class="card-body">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
            <div>
                <strong>Laravel Version:</strong><br>
                <span style="color: #6b7280;"><?php echo e(app()->version()); ?></span>
            </div>
            <div>
                <strong>PHP Version:</strong><br>
                <span style="color: #6b7280;"><?php echo e(PHP_VERSION); ?></span>
            </div>
            <div>
                <strong>Environment:</strong><br>
                <span style="color: #6b7280;"><?php echo e(app()->environment()); ?></span>
            </div>
            <div>
                <strong>Last Login:</strong><br>
                <span style="color: #6b7280;">
                    <?php if(Auth::guard('admin')->user()->last_login_at): ?>
                        <?php echo e(Auth::guard('admin')->user()->last_login_at->diffForHumans()); ?>

                    <?php else: ?>
                        First time login
                    <?php endif; ?>
                </span>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\mobile-app-dev\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>