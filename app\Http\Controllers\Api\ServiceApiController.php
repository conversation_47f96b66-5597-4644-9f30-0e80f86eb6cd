<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Service;
use Illuminate\Http\Request;

class ServiceApiController extends Controller
{
    /**
     * Display a listing of services
     */
    public function index()
    {
        try {
            $services = Service::active()
                ->ordered()
                ->get()
                ->map(function ($service) {
                    return [
                        'id' => $service->id,
                        'title' => $service->title,
                        'slug' => $service->slug,
                        'description' => $service->description,
                        'price_from' => $service->price_from,
                        'is_featured' => $service->is_featured,
                        'image_url' => $service->image_url,
                        'icon_url' => $service->icon_url,
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $services,
                'message' => 'Services retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving services: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified service
     */
    public function show($id)
    {
        try {
            $service = Service::active()->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $service->id,
                    'title' => $service->title,
                    'slug' => $service->slug,
                    'description' => $service->description,
                    'content' => $service->content,
                    'price_from' => $service->price_from,
                    'is_featured' => $service->is_featured,
                    'image_url' => $service->image_url,
                    'icon_url' => $service->icon_url,
                    'meta_data' => $service->meta_data,
                ],
                'message' => 'Service retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Service not found'
            ], 404);
        }
    }
}
