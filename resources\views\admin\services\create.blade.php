@extends('layouts.admin')

@section('title', 'Add New Service')

@section('content')
<div class="flex justify-between items-center mb-4">
    <h1 style="font-size: 1.5rem; font-weight: 600; color: #1f2937;">Add New Service</h1>
    <a href="{{ route('admin.services.index') }}" class="btn btn-secondary">
        ← Back to Services
    </a>
</div>

<div class="card">
    <div class="card-header">
        <h3>Service Information</h3>
    </div>
    <div class="card-body">
        <form action="{{ route('admin.services.store') }}" method="POST" enctype="multipart/form-data">
            @csrf
            
            <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem;">
                <!-- Main Information -->
                <div>
                    <div class="form-group">
                        <label for="title" class="form-label">Service Title *</label>
                        <input 
                            type="text" 
                            id="title" 
                            name="title" 
                            value="{{ old('title') }}" 
                            class="form-control" 
                            required
                            placeholder="e.g., Residential Construction"
                        >
                        @error('title')
                            <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="description" class="form-label">Short Description *</label>
                        <textarea 
                            id="description" 
                            name="description" 
                            rows="3" 
                            class="form-control" 
                            required
                            placeholder="Brief description of the service (max 500 characters)"
                        >{{ old('description') }}</textarea>
                        @error('description')
                            <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="content" class="form-label">Detailed Content</label>
                        <textarea 
                            id="content" 
                            name="content" 
                            rows="8" 
                            class="form-control"
                            placeholder="Detailed description of the service, what's included, process, etc."
                        >{{ old('content') }}</textarea>
                        @error('content')
                            <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;">{{ $message }}</p>
                        @enderror
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label for="price_from" class="form-label">Starting Price ($)</label>
                            <input 
                                type="number" 
                                id="price_from" 
                                name="price_from" 
                                value="{{ old('price_from') }}" 
                                class="form-control" 
                                min="0" 
                                step="0.01"
                                placeholder="e.g., 5000.00"
                            >
                            @error('price_from')
                                <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="sort_order" class="form-label">Sort Order</label>
                            <input 
                                type="number" 
                                id="sort_order" 
                                name="sort_order" 
                                value="{{ old('sort_order', 0) }}" 
                                class="form-control" 
                                min="0"
                                placeholder="0"
                            >
                            @error('sort_order')
                                <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;">{{ $message }}</p>
                            @enderror
                            <small style="color: #6b7280; font-size: 0.75rem;">Lower numbers appear first</small>
                        </div>
                    </div>
                </div>

                <!-- Images and Settings -->
                <div>
                    <div class="form-group">
                        <label for="image" class="form-label">Service Image</label>
                        <input 
                            type="file" 
                            id="image" 
                            name="image" 
                            class="form-control" 
                            accept="image/*"
                        >
                        @error('image')
                            <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;">{{ $message }}</p>
                        @enderror
                        <small style="color: #6b7280; font-size: 0.75rem;">Max 2MB, JPG/PNG/GIF</small>
                    </div>

                    <div class="form-group">
                        <label for="icon" class="form-label">Service Icon</label>
                        <input 
                            type="file" 
                            id="icon" 
                            name="icon" 
                            class="form-control" 
                            accept="image/*"
                        >
                        @error('icon')
                            <p style="color: #dc2626; font-size: 0.875rem; margin-top: 0.25rem;">{{ $message }}</p>
                        @enderror
                        <small style="color: #6b7280; font-size: 0.75rem;">Max 1MB, JPG/PNG/GIF/SVG</small>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Settings</label>
                        
                        <div style="margin-bottom: 0.5rem;">
                            <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                <input 
                                    type="checkbox" 
                                    name="is_active" 
                                    value="1" 
                                    {{ old('is_active', true) ? 'checked' : '' }}
                                >
                                <span>Active (visible on website)</span>
                            </label>
                        </div>
                        
                        <div>
                            <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                <input 
                                    type="checkbox" 
                                    name="is_featured" 
                                    value="1" 
                                    {{ old('is_featured') ? 'checked' : '' }}
                                >
                                <span>Featured (show on homepage)</span>
                            </label>
                        </div>
                    </div>

                    <div style="background: #f9fafb; padding: 1rem; border-radius: 0.375rem; margin-top: 1rem;">
                        <h4 style="font-size: 0.875rem; font-weight: 600; margin-bottom: 0.5rem; color: #374151;">Tips:</h4>
                        <ul style="font-size: 0.75rem; color: #6b7280; margin-left: 1rem;">
                            <li>Use clear, descriptive titles</li>
                            <li>Keep descriptions concise but informative</li>
                            <li>High-quality images improve engagement</li>
                            <li>Featured services appear on the homepage</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div style="border-top: 1px solid #e5e7eb; margin-top: 2rem; padding-top: 1rem;">
                <div class="flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        💾 Create Service
                    </button>
                    <a href="{{ route('admin.services.index') }}" class="btn btn-secondary">
                        Cancel
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
    // Auto-generate slug preview (optional enhancement)
    document.getElementById('title').addEventListener('input', function() {
        const title = this.value;
        const slug = title.toLowerCase()
                         .replace(/[^a-z0-9]+/g, '-')
                         .replace(/^-+|-+$/g, '');
        
        // You could show a slug preview here if desired
        console.log('Generated slug:', slug);
    });
</script>
@endpush
