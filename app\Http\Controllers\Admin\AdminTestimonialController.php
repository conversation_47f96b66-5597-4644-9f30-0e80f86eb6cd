<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Testimonial;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class AdminTestimonialController extends Controller
{
    /**
     * Display a listing of testimonials
     */
    public function index(Request $request)
    {
        $query = Testimonial::query();

        // Search
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('client_name', 'like', "%{$search}%")
                  ->orWhere('client_company', 'like', "%{$search}%")
                  ->orWhere('testimonial', 'like', "%{$search}%");
            });
        }

        // Filter by rating
        if ($request->has('rating') && $request->rating !== '') {
            $query->where('rating', $request->rating);
        }

        // Filter by featured
        if ($request->has('featured') && $request->featured === '1') {
            $query->where('is_featured', true);
        }

        // Filter by status
        if ($request->has('status')) {
            if ($request->status === 'active') {
                $query->where('is_active', true);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        $testimonials = $query->orderBy('sort_order', 'asc')
                             ->orderBy('created_at', 'desc')
                             ->paginate(20);

        return view('admin.testimonials.index', compact('testimonials'));
    }

    /**
     * Show the form for creating a new testimonial
     */
    public function create()
    {
        return view('admin.testimonials.create');
    }

    /**
     * Store a newly created testimonial
     */
    public function store(Request $request)
    {
        $request->validate([
            'client_name' => 'required|string|max:255',
            'client_title' => 'nullable|string|max:255',
            'client_company' => 'nullable|string|max:255',
            'testimonial' => 'required|string|max:2000',
            'rating' => 'required|integer|min:1|max:5',
            'project_title' => 'nullable|string|max:255',
            'sort_order' => 'nullable|integer|min:0',
            'client_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:1024',
        ]);

        $data = $request->only([
            'client_name', 'client_title', 'client_company', 'testimonial',
            'rating', 'project_title', 'sort_order'
        ]);

        $data['is_featured'] = $request->has('is_featured');
        $data['is_active'] = $request->has('is_active');

        // Handle client image upload
        if ($request->hasFile('client_image')) {
            $imagePath = $request->file('client_image')->store('testimonials', 'public');
            $data['client_image'] = basename($imagePath);
        }

        Testimonial::create($data);

        return redirect()->route('admin.testimonials.index')
                        ->with('success', 'Testimonial created successfully!');
    }

    /**
     * Display the specified testimonial
     */
    public function show(Testimonial $testimonial)
    {
        return view('admin.testimonials.show', compact('testimonial'));
    }

    /**
     * Show the form for editing the specified testimonial
     */
    public function edit(Testimonial $testimonial)
    {
        return view('admin.testimonials.edit', compact('testimonial'));
    }

    /**
     * Update the specified testimonial
     */
    public function update(Request $request, Testimonial $testimonial)
    {
        $request->validate([
            'client_name' => 'required|string|max:255',
            'client_title' => 'nullable|string|max:255',
            'client_company' => 'nullable|string|max:255',
            'testimonial' => 'required|string|max:2000',
            'rating' => 'required|integer|min:1|max:5',
            'project_title' => 'nullable|string|max:255',
            'sort_order' => 'nullable|integer|min:0',
            'client_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:1024',
        ]);

        $data = $request->only([
            'client_name', 'client_title', 'client_company', 'testimonial',
            'rating', 'project_title', 'sort_order'
        ]);

        $data['is_featured'] = $request->has('is_featured');
        $data['is_active'] = $request->has('is_active');

        // Handle client image upload
        if ($request->hasFile('client_image')) {
            // Delete old image
            if ($testimonial->client_image) {
                Storage::disk('public')->delete('testimonials/' . $testimonial->client_image);
            }
            
            $imagePath = $request->file('client_image')->store('testimonials', 'public');
            $data['client_image'] = basename($imagePath);
        }

        $testimonial->update($data);

        return redirect()->route('admin.testimonials.index')
                        ->with('success', 'Testimonial updated successfully!');
    }

    /**
     * Remove the specified testimonial
     */
    public function destroy(Testimonial $testimonial)
    {
        // Delete associated image
        if ($testimonial->client_image) {
            Storage::disk('public')->delete('testimonials/' . $testimonial->client_image);
        }

        $testimonial->delete();

        return redirect()->route('admin.testimonials.index')
                        ->with('success', 'Testimonial deleted successfully!');
    }
}
