@extends('layouts.app')

@section('title', 'Our Services')
@section('description', 'Explore our comprehensive construction services including residential, commercial construction, renovations, and project management.')

@section('content')
<!-- Hero Section -->
<section style="background: linear-gradient(135deg, #1f2937 0%, #374151 100%); color: white; padding: 4rem 0; text-align: center;">
    <div class="container">
        <h1 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 1rem;">
            Our Construction Services
        </h1>
        <p style="font-size: 1.125rem; opacity: 0.9; max-width: 600px; margin: 0 auto;">
            Professional construction services tailored to meet your specific needs and exceed your expectations.
        </p>
    </div>
</section>

<!-- Featured Services -->
@if($featuredServices->count() > 0)
<section style="padding: 4rem 0; background: #f9fafb;">
    <div class="container">
        <div class="text-center mb-8">
            <h2 style="font-size: 2rem; font-weight: 700; margin-bottom: 1rem; color: #1f2937;">
                Featured Services
            </h2>
            <p style="color: #6b7280;">Our most popular construction services</p>
        </div>
        
        <div class="grid grid-cols-3">
            @foreach($featuredServices as $service)
            <div class="card">
                @if($service->image)
                    <img src="{{ $service->image_url }}" alt="{{ $service->title }}" style="width: 100%; height: 200px; object-fit: cover;">
                @else
                    <div style="width: 100%; height: 200px; background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%); display: flex; align-items: center; justify-content: center; font-size: 3rem;">
                        🔧
                    </div>
                @endif
                
                <div class="p-6">
                    <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 0.5rem; color: #1f2937;">
                        {{ $service->title }}
                    </h3>
                    <p style="color: #6b7280; margin-bottom: 1rem;">
                        {{ $service->description }}
                    </p>
                    @if($service->price_from)
                        <p style="font-weight: 600; color: #2563eb; margin-bottom: 1rem;">
                            Starting from ${{ number_format($service->price_from) }}
                        </p>
                    @endif
                    <a href="{{ route('services.show', $service->slug) }}" class="btn btn-primary" style="width: 100%;">
                        Learn More
                    </a>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- All Services -->
<section style="padding: 4rem 0;">
    <div class="container">
        <div class="text-center mb-8">
            <h2 style="font-size: 2rem; font-weight: 700; margin-bottom: 1rem; color: #1f2937;">
                All Services
            </h2>
            <p style="color: #6b7280;">Complete range of construction services</p>
        </div>
        
        @if($services->count() > 0)
            <div class="grid grid-cols-3">
                @foreach($services as $service)
                <div class="card">
                    @if($service->image)
                        <img src="{{ $service->image_url }}" alt="{{ $service->title }}" style="width: 100%; height: 200px; object-fit: cover;">
                    @else
                        <div style="width: 100%; height: 200px; background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%); display: flex; align-items: center; justify-content: center; font-size: 3rem;">
                            @if($service->icon)
                                <img src="{{ $service->icon_url }}" alt="{{ $service->title }}" style="width: 3rem; height: 3rem;">
                            @else
                                🔧
                            @endif
                        </div>
                    @endif
                    
                    <div class="p-6">
                        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 0.5rem;">
                            <h3 style="font-size: 1.25rem; font-weight: 600; color: #1f2937;">
                                {{ $service->title }}
                            </h3>
                            @if($service->is_featured)
                                <span style="background: #059669; color: white; font-size: 0.75rem; padding: 0.25rem 0.5rem; border-radius: 9999px;">
                                    Featured
                                </span>
                            @endif
                        </div>
                        
                        <p style="color: #6b7280; margin-bottom: 1rem;">
                            {{ $service->description }}
                        </p>
                        
                        @if($service->price_from)
                            <p style="font-weight: 600; color: #2563eb; margin-bottom: 1rem;">
                                Starting from ${{ number_format($service->price_from) }}
                            </p>
                        @endif
                        
                        <a href="{{ route('services.show', $service->slug) }}" class="btn btn-primary" style="width: 100%;">
                            Learn More
                        </a>
                    </div>
                </div>
                @endforeach
            </div>
            
            <!-- Pagination -->
            @if($services->hasPages())
                <div style="margin-top: 3rem; text-align: center;">
                    {{ $services->links() }}
                </div>
            @endif
        @else
            <div style="text-align: center; padding: 3rem; color: #6b7280;">
                <p style="font-size: 1.125rem;">No services available at the moment.</p>
                <p>Please check back later or contact us for more information.</p>
            </div>
        @endif
    </div>
</section>

<!-- Call to Action -->
<section style="padding: 4rem 0; background: #2563eb; color: white; text-align: center;">
    <div class="container">
        <h2 style="font-size: 2rem; font-weight: 700; margin-bottom: 1rem;">
            Need a Custom Solution?
        </h2>
        <p style="font-size: 1.125rem; margin-bottom: 2rem; opacity: 0.9;">
            Don't see exactly what you're looking for? We provide custom construction solutions tailored to your specific needs.
        </p>
        <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
            <a href="{{ route('contact') }}" class="btn btn-primary" style="background: white; color: #2563eb;">
                Get Custom Quote
            </a>
            <a href="tel:******-567-8900" class="btn btn-secondary" style="background: transparent; border: 2px solid white;">
                📞 Call Us Now
            </a>
        </div>
    </div>
</section>
@endsection
