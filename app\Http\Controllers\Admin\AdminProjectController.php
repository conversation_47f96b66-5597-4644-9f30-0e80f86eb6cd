<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class AdminProjectController extends Controller
{
    /**
     * Display a listing of projects
     */
    public function index(Request $request)
    {
        $query = Project::query();

        // Search
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('client_name', 'like', "%{$search}%")
                  ->orWhere('location', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->has('status') && $request->status !== '') {
            $query->where('status', $request->status);
        }

        // Filter by featured
        if ($request->has('featured') && $request->featured === '1') {
            $query->where('is_featured', true);
        }

        $projects = $query->orderBy('sort_order', 'asc')
                         ->orderBy('completion_date', 'desc')
                         ->paginate(20);

        return view('admin.projects.index', compact('projects'));
    }

    /**
     * Show the form for creating a new project
     */
    public function create()
    {
        return view('admin.projects.create');
    }

    /**
     * Store a newly created project
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:500',
            'content' => 'nullable|string',
            'client_name' => 'nullable|string|max:255',
            'location' => 'nullable|string|max:255',
            'start_date' => 'nullable|date',
            'completion_date' => 'nullable|date|after_or_equal:start_date',
            'project_value' => 'nullable|numeric|min:0',
            'status' => 'required|in:planning,in_progress,completed',
            'sort_order' => 'nullable|integer|min:0',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->only([
            'title', 'description', 'content', 'client_name', 'location',
            'start_date', 'completion_date', 'project_value', 'status', 'sort_order'
        ]);

        // Generate slug
        $data['slug'] = Str::slug($request->title);
        
        // Ensure unique slug
        $originalSlug = $data['slug'];
        $counter = 1;
        while (Project::where('slug', $data['slug'])->exists()) {
            $data['slug'] = $originalSlug . '-' . $counter;
            $counter++;
        }

        $data['is_featured'] = $request->has('is_featured');
        $data['is_active'] = $request->has('is_active');

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $imagePath = $request->file('featured_image')->store('projects', 'public');
            $data['featured_image'] = basename($imagePath);
        }

        // Handle multiple images upload
        $imageNames = [];
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $imagePath = $image->store('projects', 'public');
                $imageNames[] = basename($imagePath);
            }
        }
        $data['images'] = $imageNames;

        Project::create($data);

        return redirect()->route('admin.projects.index')
                        ->with('success', 'Project created successfully!');
    }

    /**
     * Display the specified project
     */
    public function show(Project $project)
    {
        return view('admin.projects.show', compact('project'));
    }

    /**
     * Show the form for editing the specified project
     */
    public function edit(Project $project)
    {
        return view('admin.projects.edit', compact('project'));
    }

    /**
     * Update the specified project
     */
    public function update(Request $request, Project $project)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string|max:500',
            'content' => 'nullable|string',
            'client_name' => 'nullable|string|max:255',
            'location' => 'nullable|string|max:255',
            'start_date' => 'nullable|date',
            'completion_date' => 'nullable|date|after_or_equal:start_date',
            'project_value' => 'nullable|numeric|min:0',
            'status' => 'required|in:planning,in_progress,completed',
            'sort_order' => 'nullable|integer|min:0',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->only([
            'title', 'description', 'content', 'client_name', 'location',
            'start_date', 'completion_date', 'project_value', 'status', 'sort_order'
        ]);

        // Update slug if title changed
        if ($project->title !== $request->title) {
            $data['slug'] = Str::slug($request->title);
            
            // Ensure unique slug
            $originalSlug = $data['slug'];
            $counter = 1;
            while (Project::where('slug', $data['slug'])->where('id', '!=', $project->id)->exists()) {
                $data['slug'] = $originalSlug . '-' . $counter;
                $counter++;
            }
        }

        $data['is_featured'] = $request->has('is_featured');
        $data['is_active'] = $request->has('is_active');

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            // Delete old featured image
            if ($project->featured_image) {
                Storage::disk('public')->delete('projects/' . $project->featured_image);
            }
            
            $imagePath = $request->file('featured_image')->store('projects', 'public');
            $data['featured_image'] = basename($imagePath);
        }

        // Handle multiple images upload
        if ($request->hasFile('images')) {
            $imageNames = $project->images ?? [];
            
            foreach ($request->file('images') as $image) {
                $imagePath = $image->store('projects', 'public');
                $imageNames[] = basename($imagePath);
            }
            
            $data['images'] = $imageNames;
        }

        $project->update($data);

        return redirect()->route('admin.projects.index')
                        ->with('success', 'Project updated successfully!');
    }

    /**
     * Remove the specified project
     */
    public function destroy(Project $project)
    {
        // Delete associated files
        if ($project->featured_image) {
            Storage::disk('public')->delete('projects/' . $project->featured_image);
        }
        
        if ($project->images) {
            foreach ($project->images as $image) {
                Storage::disk('public')->delete('projects/' . $image);
            }
        }

        $project->delete();

        return redirect()->route('admin.projects.index')
                        ->with('success', 'Project deleted successfully!');
    }

    /**
     * Remove a specific image from project
     */
    public function removeImage(Project $project, $imageIndex)
    {
        $images = $project->images ?? [];
        
        if (isset($images[$imageIndex])) {
            // Delete the file
            Storage::disk('public')->delete('projects/' . $images[$imageIndex]);
            
            // Remove from array
            unset($images[$imageIndex]);
            $images = array_values($images); // Re-index array
            
            // Update project
            $project->update(['images' => $images]);
            
            return response()->json(['success' => true]);
        }
        
        return response()->json(['success' => false], 404);
    }
}
