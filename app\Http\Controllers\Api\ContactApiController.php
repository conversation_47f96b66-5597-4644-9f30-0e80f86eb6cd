<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ContactSubmission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ContactApiController extends Controller
{
    /**
     * Store a new contact form submission
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'phone' => 'nullable|string|max:20',
                'subject' => 'nullable|string|max:255',
                'message' => 'required|string|max:2000',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $submission = ContactSubmission::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'subject' => $request->subject ?: 'Mobile App Inquiry',
                'message' => $request->message,
                'source' => 'mobile_app',
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $submission->id,
                    'name' => $submission->name,
                    'email' => $submission->email,
                    'subject' => $submission->subject,
                    'submitted_at' => $submission->created_at->format('Y-m-d H:i:s'),
                ],
                'message' => 'Thank you for your message! We will get back to you soon.'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error submitting contact form. Please try again later.'
            ], 500);
        }
    }

    /**
     * Get contact information
     */
    public function info()
    {
        return response()->json([
            'success' => true,
            'data' => [
                'company_name' => 'Flori Construction Ltd',
                'email' => '<EMAIL>',
                'phone' => '******-567-8900',
                'address' => '123 Construction Ave, Building City, BC 12345',
                'business_hours' => [
                    'monday' => '8:00 AM - 6:00 PM',
                    'tuesday' => '8:00 AM - 6:00 PM',
                    'wednesday' => '8:00 AM - 6:00 PM',
                    'thursday' => '8:00 AM - 6:00 PM',
                    'friday' => '8:00 AM - 6:00 PM',
                    'saturday' => '9:00 AM - 4:00 PM',
                    'sunday' => 'Closed',
                ],
                'social_media' => [
                    'facebook' => 'https://facebook.com/floriconstructionltd',
                    'instagram' => 'https://instagram.com/floriconstructionltd',
                    'linkedin' => 'https://linkedin.com/company/floriconstructionltd',
                ],
                'emergency_contact' => '******-567-8911',
            ],
            'message' => 'Contact information retrieved successfully'
        ]);
    }
}
