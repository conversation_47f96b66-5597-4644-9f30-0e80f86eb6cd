<?php

namespace App\Http\Controllers;

use App\Models\Service;
use App\Models\Project;
use App\Models\Testimonial;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Display the homepage
     */
    public function index()
    {
        $featuredServices = Service::active()->featured()->ordered()->take(3)->get();
        $featuredProjects = Project::active()->featured()->ordered()->take(3)->get();
        $featuredTestimonials = Testimonial::active()->featured()->ordered()->take(3)->get();

        return view('home', compact('featuredServices', 'featuredProjects', 'featuredTestimonials'));
    }

    /**
     * Display the about page
     */
    public function about()
    {
        $stats = [
            'projects_completed' => Project::where('status', 'completed')->count(),
            'years_experience' => 15,
            'satisfied_clients' => Testimonial::count(),
            'team_members' => 25,
        ];

        return view('about', compact('stats'));
    }
}
